server:
  port: 8080
spring:
  servlet:
    multipart:
      max-file-size: 10MB
      max-request-size: 100MB
      enabled: true
      file-size-threshold: 1MB
  profiles:
    # LangFuse和可观测配置以及专业知识库配置
    include: observability, kb
  application:
    name: spring-ai-alibaba-deepresearch
  # Redis 配置
  data:
    redis:
      enabled: false
      host: localhost
      port: 6379
      password: ${REDIS-PASSWORD}
      timeout: 3000 # 连接超时时间（毫秒）
      lettuce:
        pool:
          max-active: 8
          max-idle: 8
          min-idle: 0
          max-wait: -1ms
  ai:
    dashscope:
      api-key: ${AI_DASHSCOPE_API_KEY}
      base-url: https://dashscope.aliyuncs.com
      embedding:
        options:
          model: text-embedding-v1

    mcp:
      client:
        enabled: false
        type: ASYNC
    alibaba:
      toolcalling:
        baidu:
          search:
            enabled: true
        tavilysearch:
          api-key: ${TAVILY_API_KEY}
          enabled: true
        jinacrawler:
          enabled: false
          api-key: ${JINA_API_KEY}
        serpapi:
          api-key: ${SERPAPI_KEY}
          enabled: true
        aliyunaisearch:
          api-key: ${ALIYUN_AI_SEARCH_API_KEY}
          base-url: ${ALIYUN_AI_SEARCH_BASE_URL}
          enabled: true
        # 学术研究工具调用配置
        openalex:
          enabled: false
        # 旅游生活工具调用配置
        opentripmap:
          enabled: false
          api-key: ${OPENTRIPMAP_API_KEY}
        # 百科知识工具调用配置
        wikipedia:
          enabled: false
        # 数据分析工具调用配置
        worldbankdata:
          enabled: false
        googlescholar:
          enabled: false
      deepresearch:
        # 图执行的最大迭代次数
        max-iterations: 50
        # 定义项目可以使用的搜索引擎
        search-list:
          - tavily
          - aliyun
          - baidu
          - serpapi
        smart-agents:
          enabled: false
          search-platform-mapping:
            academic_research:
              primary: openalex
            lifestyle_travel:
              primary: opentripmap
            encyclopedia:
              primary: wikipedia
            data_analysis:
              primary: worldbankdata
            general_research:
              primary: tavily
        mcp:
          enabled: false
          config-location: classpath:mcp-config.json
        parallel-node-count:
          researcher: 4
          coder: 4
        rag:
          enabled: false
          # 可以设置为 'simple' 或 'elasticsearch'
          vector-store-type: simple
          data:
            # 启动时加载 classpath下data目录中的所有文件
            locations:
              - "classpath:/data/"
          # Elasticsearch配置
          #elasticsearch:
          #  uris: http://localhost:9200
          #  similarity-function: cosine

        # 报告导出路径配置
        export:
          path: ${AI_DEEPRESEARCH_EXPORT_PATH}
        reflection:
          enabled: true
          max-attempts: 1

logging:
  level:
    com.alibaba.cloud.ai.example.deepresearch: debug

