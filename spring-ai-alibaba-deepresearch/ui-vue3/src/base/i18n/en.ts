/*
 * Copyright 2024-2026 the original author or authors.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
import type { I18nType } from './type.ts'

const words: I18nType = {
  conversation: 'Conversation',
  plan: 'Plan Execution',
  backHome: 'Back to Home',
  noPageTip: 'The page you are looking for does not exist.',
  loginDomain: {
    username: 'Userna<PERSON>',
    password: 'Password',
    login: 'Login',
    title: 'Log in',
  },
  welcome: 'Hi',
  create_new_conversation: 'New Conversation',
  help: 'Help',
  chat: 'Chat',
  knowledge_base: 'Knowledge Base',
  system_config: 'System Config',
  knowledge_management: 'Knowledge Management',
  document_upload: 'Document Upload',
  knowledge_search: 'Knowledge Search',
}

export default words
